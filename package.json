{"name": "personal-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@mdx-js/loader": "^3.1.1", "@mdx-js/react": "^3.1.1", "@next/mdx": "^15.5.2", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gray-matter": "^4.0.3", "lucide-react": "^0.536.0", "next": "15.4.6", "next-mdx-remote": "^5.0.0", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.5.0", "server-only": "^0.0.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.16", "eslint": "^9", "eslint-config-next": "15.4.6", "postcss": "^8.4.31", "tailwindcss": "^3.4.1", "typescript": "^5"}}